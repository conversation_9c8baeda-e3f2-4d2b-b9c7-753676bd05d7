#!/usr/bin/env python3
"""
<PERSON>ript to fix all syntax errors in analyzer.py
"""

import re

def fix_syntax_errors(file_path):
    """Fix all syntax errors in the analyzer.py file"""

    with open(file_path, 'r') as f:
        content = f.read()

    print(f"Original file size: {len(content)} characters")

    # Fix 1: Remove await from log_error_with_context calls
    content = re.sub(r'await log_error_with_context\(', 'log_error_with_context(', content)
    print("✓ Fixed await log_error_with_context calls")

    # Fix 2: Fix malformed f-string syntax errors like f"message {var, "context"}"
    # Pattern to match malformed f-strings with extra commas and quotes
    malformed_patterns = [
        # Fix f"text {var, "context"}" -> f"text {var}", "context"
        (r'f"([^"]*\{[^}]*), "([^"]*)"}"', r'f"\1}", "\2"'),
        # Fix f"text {var, "context")}" -> f"text {var}", "context"
        (r'f"([^"]*\{[^}]*), "([^"]*)"}\)"', r'f"\1}", "\2")'),
        # Fix Exception(f"text {var, "context"}" -> Exception(f"text {var}"), "context"
        (r'Exception\(f"([^"]*\{[^}]*), "([^"]*)"}"', r'Exception(f"\1}"), "\2"'),
        # Fix Exception(f"text {var, "context")}" -> Exception(f"text {var}"), "context"
        (r'Exception\(f"([^"]*\{[^}]*), "([^"]*)"}\)"', r'Exception(f"\1}"), "\2")'),
    ]

    for pattern, replacement in malformed_patterns:
        old_content = content
        content = re.sub(pattern, replacement, content)
        if content != old_content:
            print(f"✓ Fixed malformed f-string pattern: {pattern[:50]}...")

    # Fix 3: Fix specific syntax errors found in the file
    specific_fixes = [
        # Fix missing closing parenthesis
        ('log_error_with_context(Exception(f"Summary generation failed: {str(e), "performance"}")',
         'log_error_with_context(Exception(f"Summary generation failed: {str(e)}"), "performance")'),

        # Fix other specific malformed calls
        ('log_error_with_context(Exception(f"Missing required metric: {str(e), "performance"}")',
         'log_error_with_context(Exception(f"Missing required metric: {str(e)}"), "performance")'),

        ('log_error_with_context(Exception(f"Summary formatting failed: {str(e), "performance"}")',
         'log_error_with_context(Exception(f"Summary formatting failed: {str(e)}"), "performance")'),

        ('log_error_with_context(Exception(f"Save operation failed: {str(e), "storage"}")',
         'log_error_with_context(Exception(f"Save operation failed: {str(e)}"), "storage")'),

        ('log_error_with_context(Exception(f"Results processing failed: {str(e), "storage"}")',
         'log_error_with_context(Exception(f"Results processing failed: {str(e)}"), "storage")'),

        ('log_error_with_context(Exception(f"Metrics preparation failed: {str(e), "storage"}")',
         'log_error_with_context(Exception(f"Metrics preparation failed: {str(e)}"), "storage")'),
    ]

    for old_text, new_text in specific_fixes:
        if old_text in content:
            content = content.replace(old_text, new_text)
            print(f"✓ Fixed specific syntax error: {old_text[:50]}...")

    # Fix 4: Fix all remaining malformed f-string patterns
    # This is a comprehensive fix for all f-string syntax errors

    # Use simple string replacement for the most common patterns
    replacements = [
        # Fix the most common malformed f-string patterns
        (', "performance"}"', '}"), "performance"'),
        (', "storage"}"', '}"), "storage"'),
        (', "device"}"', '}"), "device"'),
        (', "analysis"}"', '}"), "analysis"'),
        (', "locator"}"', '}"), "locator"'),
        (', "app"}"', '}"), "app"'),
        (', "hierarchy"}"', '}"), "hierarchy"'),
        (', "element"}"', '}"), "element"'),
        (', "bounds_str"}"', '}"), "bounds_str"'),
        # Fix specific timeout patterns
        ('), "device")"', '}"), "device")'),
        ('), "app")"', '}"), "app")'),
        ('), "performance")"', '}"), "performance")'),
        ('), "storage")"', '}"), "storage")'),
    ]

    for old_pattern, new_pattern in replacements:
        if old_pattern in content:
            content = content.replace(old_pattern, new_pattern)
            print(f"✓ Fixed pattern: {old_pattern} -> {new_pattern}")

    # Pattern 4: Fix any remaining malformed patterns with regex
    malformed_fstring_patterns = [
        # Fix any remaining f"...{var, "context"}" patterns
        (r'f"([^"]*\{[^}]*), "([^"]*)"}\)', r'f"\1}"), "\2")'),
        (r'f"([^"]*\{[^}]*), "([^"]*)"}"', r'f"\1}"), "\2"'),
        # Fix timeout and error message patterns
        (r'(timeout[^}]*), "([^"]*)"}\)', r'\1}"), "\2")'),
        (r'(failed[^}]*), "([^"]*)"}\)', r'\1}"), "\2")'),
        (r'(error[^}]*), "([^"]*)"}\)', r'\1}"), "\2")'),
    ]

    for pattern, replacement in malformed_fstring_patterns:
        old_content = content
        content = re.sub(pattern, replacement, content)
        if content != old_content:
            print(f"✓ Fixed malformed f-string pattern: {pattern[:50]}...")

    # Write back to file
    with open(file_path, 'w') as f:
        f.write(content)

    print(f"✓ Fixed syntax errors in {file_path}")
    print(f"Final file size: {len(content)} characters")

if __name__ == "__main__":
    fix_syntax_errors("src/mobile/analyzer.py")
