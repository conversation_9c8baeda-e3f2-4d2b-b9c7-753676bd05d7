#!/usr/bin/env python3
"""
Comprehensive fix for all syntax errors in analyzer.py
"""

import re
import sys

def fix_all_syntax_errors(file_path):
    """Fix all syntax errors in the analyzer.py file"""
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    print(f"Original file size: {len(content)} characters")
    
    # Fix 1: Remove all await from log_error_with_context calls
    content = re.sub(r'await log_error_with_context\(', 'log_error_with_context(', content)
    print("✓ Fixed await log_error_with_context calls")
    
    # Fix 2: Fix all malformed f-string patterns systematically
    
    # Pattern: f"text {var, "context"}" -> f"text {var}", "context"
    # This handles the most common malformed f-string pattern
    malformed_patterns = [
        # Fix f"...{var, "context"}" patterns
        (r'f"([^"]*\{[^}]*), "([^"]*)"}"', r'f"\1}", "\2"'),
        (r'f"([^"]*\{[^}]*), "([^"]*)"}\)', r'f"\1}", "\2")'),
        
        # Fix Exception(f"...{var, "context"}" patterns
        (r'Exception\(f"([^"]*\{[^}]*), "([^"]*)"}"', r'Exception(f"\1}"), "\2"'),
        (r'Exception\(f"([^"]*\{[^}]*), "([^"]*)"}\)', r'Exception(f"\1}"), "\2")'),
        
        # Fix specific timeout/error patterns with extra }
        (r'\{max_retries\}\}', r'{max_retries}'),
        (r'\{height\}\}', r'{height}'),
        (r'\{width\}\}', r'{width}'),
        
        # Fix malformed log_error_with_context calls
        (r'log_error_with_context\(Exception\(f"([^"]*\{[^}]*), "([^"]*)"}\)', 
         r'log_error_with_context(Exception(f"\1}"), "\2")'),
    ]
    
    for pattern, replacement in malformed_patterns:
        old_content = content
        content = re.sub(pattern, replacement, content)
        if content != old_content:
            print(f"✓ Fixed malformed pattern: {pattern[:50]}...")
    
    # Fix 3: Fix specific known problematic lines with direct replacement
    specific_fixes = [
        # Fix remaining malformed f-strings
        (', "performance"}"', '}"), "performance"'),
        (', "storage"}"', '}"), "storage"'),
        (', "device"}"', '}"), "device"'),
        (', "analysis"}"', '}"), "analysis"'),
        (', "locator"}"', '}"), "locator"'),
        (', "app"}"', '}"), "app"'),
        (', "hierarchy"}"', '}"), "hierarchy"'),
        (', "element"}"', '}"), "element"'),
        (', "bounds_str"}"', '}"), "bounds_str"'),
        
        # Fix specific timeout patterns
        ('), "device")"', '}"), "device")'),
        ('), "app")"', '}"), "app")'),
        ('), "performance")"', '}"), "performance")'),
        ('), "storage")"', '}"), "storage")'),
        
        # Fix double closing braces
        ('}}"), "device")', '}"), "device")'),
        ('}}"), "app")', '}"), "app")'),
        ('}}"), "performance")', '}"), "performance")'),
        ('}}"), "storage")', '}"), "storage")'),
        
        # Fix specific malformed calls
        ('f"Invalid element type: {type(element), "device"}"', 'f"Invalid element type: {type(element)}", "device"'),
        ('f"Invalid coordinate types: ({type(fx), "device"}"', 'f"Invalid coordinate types: ({type(fx)}", "device"'),
    ]
    
    for old_text, new_text in specific_fixes:
        if old_text in content:
            content = content.replace(old_text, new_text)
            print(f"✓ Fixed specific error: {old_text[:50]}...")
    
    # Fix 4: Fix any remaining syntax issues
    # Remove any trailing empty parentheses or malformed calls
    content = re.sub(r'\)\s*\)\s*$', ')', content, flags=re.MULTILINE)
    
    # Fix any remaining double closing braces in f-strings
    content = re.sub(r'\{\{([^}]+)\}\}', r'{\1}', content)
    
    # Write back to file
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"✓ Fixed all syntax errors in {file_path}")
    print(f"Final file size: {len(content)} characters")

if __name__ == "__main__":
    fix_all_syntax_errors("src/mobile/analyzer.py")
