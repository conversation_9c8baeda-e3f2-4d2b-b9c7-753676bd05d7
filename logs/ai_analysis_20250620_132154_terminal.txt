
================================================================================
sMTm Framework - Terminal Log Session
================================================================================
Session ID: ai_analysis_20250620_132154
Start Time: 2025-06-20 13:21:54
Command: /ai-analysis
================================================================================

[13:21:54] [INFO] 🚀 AI Analysis Started for app: Unknown
[13:21:54] [INFO] ============================================================
[13:21:54] [INFO] 📱 AI Analysis - session_start: AI Analysis session started: ai_analysis_20250620_132154
[13:21:54] [INFO] 📋 === AI ANALYSIS SESSION ===
[13:21:54] [INFO]   session_id: ai_analysis_20250620_132154
[13:21:54] [INFO]   start_time: 2025-06-20T13:21:54.438866
[13:21:54] [INFO]   custom_instructions: comprehensive
[13:21:54] [INFO]   analysis_mode: comprehensive_ui_analysis
[13:21:54] [INFO] 📋 === END AI ANALYSIS SESSION ===
[13:21:54] [STDOUT] Detected OS: macOS
[13:21:54] [RICH_CONSOLE] Detected OS: macOS
[13:21:54] [INFO] 📱 AI Analysis - os_detection: Detected OS: macOS
[13:21:54] [INFO] 📋 === OS DETECTION ===
[13:21:54] [INFO]   detected_os: macOS
[13:21:54] [INFO]   detection_method: OSDetector
[13:21:54] [INFO]   timestamp: 2025-06-20T13:21:54.441243
[13:21:54] [INFO] 📋 === END OS DETECTION ===
[13:21:54] [STDOUT] Use existing installation? (y/n):
[13:21:54] [RICH_CONSOLE] Use existing installation? (y/n):
[13:21:56] [INFO] 📱 AI Analysis - installation_preference: Use existing installation: True
[13:21:56] [STDOUT] What mobile OS would you like to analyze?
[13:21:56] [RICH_CONSOLE] What mobile OS would you like to analyze?
[13:21:56] [STDOUT] 1. Android
[13:21:56] [RICH_CONSOLE] 1. Android
[13:21:56] [STDOUT] 2. iOS
[13:21:56] [RICH_CONSOLE] 2. iOS
[13:21:56] [STDOUT] Enter your choice:
[13:21:56] [RICH_CONSOLE] Enter your choice:
[13:21:57] [INFO] 📱 AI Analysis - mobile_os_selection: Selected mobile OS: android
[13:21:57] [STDOUT] 
[13:21:57] [INFO] 📱 AI Analysis - android_setup_start: Starting Android environment setup
[13:21:57] [STDOUT] Checking Android environment...
⠋ Processing command...
[13:21:57] [RICH_CONSOLE] Checking Android environment...
[13:21:57] [STDOUT] 
Android emulator is already running
⠋ Processing command...
[13:21:57] [RICH_CONSOLE] Android emulator is already running
[13:21:57] [STDOUT] 
Connecting to Android device...
⠋ Processing command...
[13:21:57] [RICH_CONSOLE] Connecting to Android device...
[13:21:57] [STDOUT] Connecting to Android device...
[13:21:57] [STDOUT] 🔍 Validating device readiness: emulator-5554
[13:21:57] [STDOUT]   📡 Testing basic shell connectivity (attempt 1/3, timeout: 8s)...
[13:21:57] [STDOUT]   ✅ Shell connectivity OK (attempt 1)
[13:21:57] [STDOUT]   🚀 Checking boot completion status...
[13:21:57] [STATUS] Processing command...
[13:21:57] [STDOUT]   ✅ Device fully booted
[13:21:57] [STDOUT]   📱 Testing device responsiveness...
[13:21:57] [STDOUT]   ✅ Device responsive (Android 14)
[13:21:57] [STDOUT] 🎉 Device emulator-5554 is ready for connection!
[13:21:57] [STDOUT] ✅ 1 device(s) fully ready
[13:21:57] [STDOUT] 📱 Found 1 device(s)
[13:21:57] [STDOUT]   Device 1: emulator-5554 (status: device)
[13:21:57] [STDOUT] 🔗 Attempting to connect to device: emulator-5554
[13:21:57] [STDOUT] 🔧 Preparing device for connection...
[13:22:00] [STDOUT] 🔄 Trying direct connection strategy (timeout: 20s)...
[13:22:00] [STDOUT]   🔌 Attempting uiautomator2 connection...
[13:22:00] [STDOUT]   🧪 Verifying connection...
[13:22:00] [STDOUT] ✓ Device connection established using direct strategy
[13:22:00] [STDOUT]   📱 Device: sdk_gphone64_arm64
[13:22:00] [STDOUT] 
✓ Android Hardware Button Controller initialized
⠏ Processing command...
[13:22:00] [RICH_CONSOLE] ✓ Android Hardware Button Controller initialized
[13:22:00] [STDOUT] 
✓ Android environment setup completed!
⠏ Processing command...
[13:22:00] [RICH_CONSOLE] ✓ Android environment setup completed!
[13:22:00] [INFO] 📋 === ANDROID SETUP SUCCESS ===
[13:22:00] [INFO]   setup_duration: 3.17s
[13:22:00] [INFO]   device_connected: False
[13:22:00] [INFO]   emulator_status: unknown
[13:22:00] [INFO]   android_version: unknown
[13:22:00] [INFO] 📋 === END ANDROID SETUP SUCCESS ===
[13:22:00] [INFO] 📱 AI Analysis - android_setup_success: Android environment setup completed
[13:22:00] [INFO] 📱 AI Analysis - app_selection_start: Starting app selection and preparation
[13:22:00] [STDOUT] 📱 Analyzing available applications...
[13:22:00] [RICH_CONSOLE] 📱 Analyzing available applications...
[13:22:01] [STDOUT] 📋 Available applications:
[13:22:01] [RICH_CONSOLE] 📋 Available applications:
[13:22:01] [STDOUT]   1. com.kemendikdasmen.rumahpendidikan (Installed)
[13:22:01] [RICH_CONSOLE]   1. com.kemendikdasmen.rumahpendidikan (Installed)
[13:22:01] [STDOUT]   2. Rumah Pendidikan (APK File)
[13:22:01] [RICH_CONSOLE]   2. Rumah Pendidikan (APK File)
[13:22:01] [STDOUT] 🎯 Auto-selected main application for custom analysis: com.kemendikdasmen.rumahpendidikan
[13:22:01] [RICH_CONSOLE] 🎯 Auto-selected main application for custom analysis: com.kemendikdasmen.rumahpendidikan
[13:22:01] [STDOUT] ✓ Selected: com.kemendikdasmen.rumahpendidikan
[13:22:01] [RICH_CONSOLE] ✓ Selected: com.kemendikdasmen.rumahpendidikan
[13:22:01] [STDOUT] 🔍 Checking if com.kemendikdasmen.rumahpendidikan is already running...
[13:22:01] [RICH_CONSOLE] 🔍 Checking if com.kemendikdasmen.rumahpendidikan is already running...
[13:22:01] [STDOUT] ✓ com.kemendikdasmen.rumahpendidikan is already running and ready for analysis!
[13:22:01] [RICH_CONSOLE] ✓ com.kemendikdasmen.rumahpendidikan is already running and ready for analysis!
[13:22:01] [STDOUT] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[13:22:01] [RICH_CONSOLE] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[13:22:01] [STDOUT] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[13:22:01] [RICH_CONSOLE] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[13:22:01] [STDOUT] 🚀 Skipping launch - proceeding directly to analysis
[13:22:01] [RICH_CONSOLE] 🚀 Skipping launch - proceeding directly to analysis
[13:22:01] [STDOUT] ✓ com.kemendikdasmen.rumahpendidikan is ready for analysis!
[13:22:01] [RICH_CONSOLE] ✓ com.kemendikdasmen.rumahpendidikan is ready for analysis!
[13:22:01] [INFO] 📱 AI Analysis - app_selected: Selected app: com.kemendikdasmen.rumahpendidikan
[13:22:01] [INFO] 📱 AI Analysis - custom_instructions: Custom analysis: comprehensive
[13:22:01] [STDOUT] 🎯 Custom Analysis Instructions: comprehensive
[13:22:01] [RICH_CONSOLE] 🎯 Custom Analysis Instructions: comprehensive
[13:22:01] [INFO] 🚀 AI Analysis Started for app: com.kemendikdasmen.rumahpendidikan
[13:22:01] [INFO] ============================================================
[13:22:01] [STDOUT] 
[13:22:01] [INFO] 📱 AI Analysis - ui_analysis_start: Starting deep UI analysis
[13:22:01] [INFO] 📋 === UI ANALYSIS CONFIGURATION ===
[13:22:01] [INFO]   custom_instructions: comprehensive
[13:22:01] [INFO]   target_elements: 3000
[13:22:01] [INFO]   analysis_mode: comprehensive
[13:22:01] [INFO]   app_package: com.kemendikdasmen.rumahpendidikan
[13:22:01] [INFO] 📋 === END UI ANALYSIS CONFIGURATION ===
[13:22:01] [STDOUT] 🔍 Starting sequential, organized UI analysis...
⠋ Processing command...
[13:22:01] [RICH_CONSOLE] 🔍 Starting sequential, organized UI analysis...
[13:22:01] [STDOUT] 
🔧 Initializing AI improvement system...
⠋ Processing command...
[13:22:01] [RICH_CONSOLE] 🔧 Initializing AI improvement system...
[13:22:01] [STDOUT] 
✅ Professional Hybrid Headless Detector initialized
⠋ Processing command...
[13:22:01] [STDOUT] 
✅ Enhanced Element Classifier with Mobile Locator Knowledge initialized
⠋ Processing command...
[13:22:01] [STDOUT] 
✅ Real-time Problem Solver initialized
⠋ Processing command...
[13:22:01] [STDOUT] 
✅ AI Self-Improvement System initialized successfully
⠋ Processing command...
[13:22:01] [RICH_CONSOLE] ✅ AI Self-Improvement System initialized successfully
[13:22:01] [STDOUT] 
🔧 AI improvement system initialization completed
⠋ Processing command...
[13:22:01] [RICH_CONSOLE] 🔧 AI improvement system initialization completed
[13:22:01] [STDOUT] 
✅ AI improvement system initialized successfully
⠋ Processing command...
[13:22:01] [RICH_CONSOLE] ✅ AI improvement system initialized successfully
[13:22:01] [STDOUT] 
🔍 robust_element_collector type: <class 'mobile.robust_element_collector.RobustElementCollector'>
⠋ Processing command...
[13:22:01] [RICH_CONSOLE] 🔍 robust_element_collector type: <class 'mobile.robust_element_collector.RobustElementCollector'>
[13:22:01] [STDOUT] 
✅ collect_all_elements_robustly method exists on analyzer
⠋ Processing command...
[13:22:01] [RICH_CONSOLE] ✅ collect_all_elements_robustly method exists on analyzer
[13:22:01] [STDOUT] 
🔍 Method type: <class 'method'>
⠋ Processing command...
[13:22:01] [RICH_CONSOLE] 🔍 Method type: <class 'method'>
[13:22:01] [STDOUT] 
📱 Current app: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[13:22:01] [RICH_CONSOLE] 📱 Current app: com.kemendikdasmen.rumahpendidikan
[13:22:01] [STDOUT] 
🎯 Target app: com.kemendikdasmen.rumahpendidikan
⠙ Processing command...
[13:22:01] [RICH_CONSOLE] 🎯 Target app: com.kemendikdasmen.rumahpendidikan
[13:22:01] [STDOUT] 
✅ Verified: Analysis starting in correct main app
⠙ Processing command...
[13:22:01] [RICH_CONSOLE] ✅ Verified: Analysis starting in correct main app
[13:22:01] [STDOUT] 
🧹 Checking for corrupted Excel files (JSON with .xlsx extension)...
⠙ Processing command...
[13:22:01] [RICH_CONSOLE] 🧹 Checking for corrupted Excel files (JSON with .xlsx extension)...
[13:22:01] [STDOUT] 
✅ No corrupted Excel files found
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] ✅ No corrupted Excel files found
[13:22:01] [INFO] 📋 === LOOP PREVENTION SYSTEM RESET ===
[13:22:01] [INFO]   emergency_exit_triggered: False
[13:22:01] [INFO]   analysis_start_time: 2025-06-20T13:22:01.505197
[13:22:01] [INFO]   timeout_minutes: 45
[13:22:01] [INFO]   consecutive_failures_threshold: 8
[13:22:01] [INFO]   reset_timestamp: 2025-06-20T13:22:01.505212
[13:22:01] [INFO] 📋 === END LOOP PREVENTION SYSTEM RESET ===
[13:22:01] [STDOUT] 
✅ Loop prevention system reset for new analysis session
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] ✅ Loop prevention system reset for new analysis session
[13:22:01] [STDOUT] 
🎯 COMPREHENSIVE MODE TRIGGERED: AI will autonomously discover ALL UI structure
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] 🎯 COMPREHENSIVE MODE TRIGGERED: AI will autonomously discover ALL UI structure
[13:22:01] [STDOUT] 
🧠 TEACHING AI: Collect complete hierarchical navigation structure without hardcoded values
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] 🧠 TEACHING AI: Collect complete hierarchical navigation structure without hardcoded values
[13:22:01] [STDOUT] 
🎯 COMPREHENSIVE MODE ACTIVATED: Collecting ALL element locators from complete UI structure
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] 🎯 COMPREHENSIVE MODE ACTIVATED: Collecting ALL element locators from complete UI structure
[13:22:01] [STDOUT] 
📋 AI will autonomously discover and analyze entire application hierarchy
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] 📋 AI will autonomously discover and analyze entire application hierarchy
[13:22:01] [STDOUT] 
🎯 Target: Complete menu structure, sub-menus, and all navigation elements
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] 🎯 Target: Complete menu structure, sub-menus, and all navigation elements
[13:22:01] [STDOUT] 
🚀 ENHANCED: 3000+ element collection with 60-minute duration
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] 🚀 ENHANCED: 3000+ element collection with 60-minute duration
[13:22:01] [STDOUT] 
📊 Performance Goals: 3000+ elements, <3% unknown, 60 minutes max
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] 📊 Performance Goals: 3000+ elements, <3% unknown, 60 minutes max
[13:22:01] [STDOUT] 
🏗️ HIERARCHICAL STRUCTURE TARGET:
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] 🏗️ HIERARCHICAL STRUCTURE TARGET:
[13:22:01] [STDOUT] 
   • Beranda with all subsections (1.1-1.15)
⠹ Processing command...
[13:22:01] [RICH_CONSOLE]    • Beranda with all subsections (1.1-1.15)
[13:22:01] [STDOUT] 
   • Ruang GTK with all sub-elements (1.4.1-1.4.19)
⠹ Processing command...
[13:22:01] [RICH_CONSOLE]    • Ruang GTK with all sub-elements (1.4.1-1.4.19)
[13:22:01] [STDOUT] 
   • Ruang Murid, Ruang Sekolah, Ruang Bahasa, etc.
⠹ Processing command...
[13:22:01] [RICH_CONSOLE]    • Ruang Murid, Ruang Sekolah, Ruang Bahasa, etc.
[13:22:01] [STDOUT] 
   • Complete navigation bar elements
⠹ Processing command...
[13:22:01] [RICH_CONSOLE]    • Complete navigation bar elements
[13:22:01] [STDOUT] 
   • External app first-page elements only
⠹ Processing command...
[13:22:01] [RICH_CONSOLE]    • External app first-page elements only
[13:22:01] [STDOUT] 
⚠️ SKIPPING INCREMENTAL ANALYSIS - Full comprehensive analysis will be performed
⠹ Processing command...
[13:22:01] [RICH_CONSOLE] ⚠️ SKIPPING INCREMENTAL ANALYSIS - Full comprehensive analysis will be performed
[13:22:01] [STDOUT] 
🎯 Primary app focus: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[13:22:01] [RICH_CONSOLE] 🎯 Primary app focus: com.kemendikdasmen.rumahpendidikan
[13:22:01] [STDOUT] 
🔒 STRICT FILTERING: Only com.kemendikdasmen.rumahpendidikan and direct external apps allowed
⠸ Processing command...
[13:22:01] [RICH_CONSOLE] 🔒 STRICT FILTERING: Only com.kemendikdasmen.rumahpendidikan and direct external apps allowed
[13:22:01] [STDOUT] 
🎯 Main app context set: com.kemendikdasmen.rumahpendidikan
⠸ Processing command...
[13:22:01] [STDOUT] 
🧠 Intelligent navigation system initialized
⠸ Processing command...
[13:22:01] [RICH_CONSOLE] 🧠 Intelligent navigation system initialized
[13:22:01] [STDOUT] 
📂 Loading existing elements from persistent file: rumahpendidikan_persistent_analysis.xlsx
⠸ Processing command...
[13:22:01] [STDOUT] 
✅ Loaded 229 existing elements from persistent file
⠴ Processing command...
[13:22:01] [STDOUT] 
✅ Persistent Excel manager initialized for com.kemendikdasmen.rumahpendidikan
⠴ Processing command...
[13:22:01] [RICH_CONSOLE] ✅ Persistent Excel manager initialized for com.kemendikdasmen.rumahpendidikan
[13:22:01] [STDOUT] 
✅ Persistent Excel file is valid: rumahpendidikan_persistent_analysis.xlsx
⠦ Processing command...
[13:22:01] [STDOUT] 
📋 Using persistent Excel storage only - no separate live save files created
⠦ Processing command...
[13:22:01] [RICH_CONSOLE] 📋 Using persistent Excel storage only - no separate live save files created
[13:22:01] [STDOUT] 
🎯 Performance Goals Tracking Started:
⠦ Processing command...
[13:22:01] [RICH_CONSOLE] 🎯 Performance Goals Tracking Started:
[13:22:01] [STDOUT] 
  • Target Elements: 3000
⠦ Processing command...
[13:22:01] [RICH_CONSOLE]   • Target Elements: 3000
[13:22:01] [STDOUT] 
  • Target Duration: 60 minutes
⠦ Processing command...
[13:22:01] [RICH_CONSOLE]   • Target Duration: 60 minutes
[13:22:01] [STDOUT] 
  • Max Unknown Elements: 3.0%
⠦ Processing command...
[13:22:01] [RICH_CONSOLE]   • Max Unknown Elements: 3.0%
[13:22:01] [STDOUT] 
🔍 Real-time problem monitoring started in background
⠦ Processing command...
[13:22:01] [STDOUT] 
🔧 Real-time problem solver monitoring started
⠦ Processing command...
[13:22:01] [RICH_CONSOLE] 🔧 Real-time problem solver monitoring started
[13:22:01] [STDOUT] 
🔍 Searching for previous analysis state for rumahpendidikan...
⠦ Processing command...
[13:22:01] [RICH_CONSOLE] 🔍 Searching for previous analysis state for rumahpendidikan...
[13:22:01] [STDOUT] 
📋 Checking persistent Excel manager for analysis state...
⠦ Processing command...
[13:22:01] [RICH_CONSOLE] 📋 Checking persistent Excel manager for analysis state...
[13:22:01] [STDOUT] 
⚠️ Could not read Analysis_State from persistent file: Worksheet named 'Analysis_State' not found
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] ⚠️ Could not read Analysis_State from persistent file: Worksheet named 'Analysis_State' not found
[13:22:01] [STDOUT] 
📂 Falling back to search for legacy live analysis files...
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] 📂 Falling back to search for legacy live analysis files...
[13:22:01] [STDOUT] 
📂 Found previous analysis state: ./data/analysis/rumahpendidikan_20250602_215601_live_analysis.xlsx
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] 📂 Found previous analysis state: ./data/analysis/rumahpendidikan_20250602_215601_live_analysis.xlsx
[13:22:01] [STDOUT] 
⚠️ Could not read file format: 'utf-8' codec can't decode byte 0xb6 in position 11: invalid start byte
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] ⚠️ Could not read file format: 'utf-8' codec can't decode byte 0xb6 in position 11: invalid start byte
[13:22:01] [STDOUT] 
🏗️ HIERARCHICAL STRUCTURE ANALYSIS STARTED
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] 🏗️ HIERARCHICAL STRUCTURE ANALYSIS STARTED
[13:22:01] [STDOUT] 
📋 Target: Complete Rumah Pendidikan UI hierarchy collection
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] 📋 Target: Complete Rumah Pendidikan UI hierarchy collection
[13:22:01] [STDOUT] 
📋 Phase 1: Comprehensive main page analysis and feature identification...
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] 📋 Phase 1: Comprehensive main page analysis and feature identification...
[13:22:01] [STDOUT] 
🔍 Collecting all elements from main page...
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] 🔍 Collecting all elements from main page...
[13:22:01] [STDOUT] 
✅ collect_all_elements_robustly method exists
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] ✅ collect_all_elements_robustly method exists
[13:22:01] [STDOUT] 
🔍 Method type: <class 'method'>
⠧ Processing command...
[13:22:01] [RICH_CONSOLE] 🔍 Method type: <class 'method'>
[13:22:02] [ERROR] ❌ ERROR in Hierarchy dump failed: object str can't be used in 'await' expression (attempt 1/3): device
[13:22:02] [ERROR] ❌ Error Type: str
[13:22:02] [ERROR] ❌ Stack Trace:
[13:22:02] [ERROR]     Traceback (most recent call last):
[13:22:02] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1383, in _safe_dump_hierarchy
[13:22:02] [ERROR]         hierarchy = await asyncio.wait_for(
[13:22:02] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[13:22:02] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[13:22:02] [ERROR]         return await fut
[13:22:02] [ERROR]                ^^^^^^^^^
[13:22:02] [ERROR]     TypeError: object str can't be used in 'await' expression
[13:22:02] [ERROR] ❌ ERROR in element collection: object NoneType can't be used in 'await' expression
[13:22:02] [ERROR] ❌ Error Type: TypeError
[13:22:02] [ERROR] ❌ Stack Trace:
[13:22:02] [ERROR]     Traceback (most recent call last):
[13:22:02] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1383, in _safe_dump_hierarchy
[13:22:02] [ERROR]         hierarchy = await asyncio.wait_for(
[13:22:02] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^
[13:22:02] [ERROR]       File "/opt/anaconda3/lib/python3.12/asyncio/tasks.py", line 520, in wait_for
[13:22:02] [ERROR]         return await fut
[13:22:02] [ERROR]                ^^^^^^^^^
[13:22:02] [ERROR]     TypeError: object str can't be used in 'await' expression
[13:22:02] [ERROR]     During handling of the above exception, another exception occurred:
[13:22:02] [ERROR]     Traceback (most recent call last):
[13:22:02] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 750, in collect_all_elements_robustly
[13:22:02] [ERROR]         hierarchy = await self._safe_dump_hierarchy()
[13:22:02] [ERROR]                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[13:22:02] [ERROR]       File "/Users/<USER>/code/python/Compare_sMTm/sMTm/src/mobile/analyzer.py", line 1402, in _safe_dump_hierarchy
[13:22:02] [ERROR]         await log_error_with_context("device", f"Hierarchy dump failed: {str(e)} (attempt {retry_count + 1}/{max_retries})")
[13:22:02] [ERROR]     TypeError: object NoneType can't be used in 'await' expression
[13:22:02] [STDOUT] 
❌ No elements found on main page
⠏ Processing command...
[13:22:02] [RICH_CONSOLE] ❌ No elements found on main page
[13:22:02] [ERROR] ❌ ERROR in Deep UI Analysis: No elements found on main page
[13:22:02] [ERROR] ❌ Error Type: Exception
[13:22:02] [ERROR] ❌ Stack Trace:
[13:22:02] [ERROR]     NoneType: None
[13:22:02] [ERROR] ❌ Additional Context:
[13:22:02] [ERROR]     error_message: No elements found on main page
[13:22:02] [ERROR]     analysis_duration: 0.74s
[13:22:02] [ERROR]     elements_found_before_failure: 0
[13:22:02] [ERROR]     step: deep_ui_analysis
[13:22:02] [INFO] 📱 AI Analysis - ui_analysis_failed: UI analysis failed: No elements found on main page
[13:22:02] [INFO] ============================================================
[13:22:02] [ERROR] ❌ AI Analysis Failed: No elements found on main page
[13:22:02] [INFO] End Time: 2025-06-20 13:22:02

================================================================================
Session End Time: 2025-06-20 13:22:02
Log File: logs/ai_analysis_20250620_132154_terminal.txt
================================================================================
